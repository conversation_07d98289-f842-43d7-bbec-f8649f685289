import asyncio
import threading
from chat_handler import <PERSON>ump<PERSON>hat<PERSON>andler
from gui import P<PERSON><PERSON><PERSON><PERSON>GUI

# Configuration
CONFIG = {
    'room_id': '2pdgDGarGrPD5y7W2BeHmz1RZjke6UouKNeDjGM3pump',
    'move_interval_seconds': 5,  # Time between moves in democracy mode
    'mode_swap_minutes': 2  # Time between democracy/anarchy mode swaps
}

def run_gui(chat_handler):
    gui = PumpPokemonGUI(chat_handler)
    gui.run()

async def main():
    # Initialize chat handler
    chat_handler = PumpChatHandler(CONFIG['room_id'], CONFIG)
    
    # Start GUI in separate thread
    gui_thread = threading.Thread(
        target=run_gui, 
        args=(chat_handler,),
        daemon=True
    )
    gui_thread.start()
    
    print("Starting Pump Plays Pokemon...")
    print(f"Mode: {chat_handler.mode}")
    print(f"Move interval: {CONFIG['move_interval_seconds']}s")
    
    # Connect to chat and start listening
    try:
        await chat_handler.connect_and_listen()
    except KeyboardInterrupt:
        print("Shutting down...")

if __name__ == "__main__":
    asyncio.run(main())