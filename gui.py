import tkinter as tk
from tkinter import ttk
import threading
import time
from datetime import datetime, timedelta

class PumpPokemonGUI:
    def __init__(self, chat_handler):
        self.chat_handler = chat_handler
        self.root = tk.Tk()
        self.root.title("Pump Plays Pokemon")
        self.root.configure(bg='green')
        self.root.geometry("600x400")
        
        # Create main frame
        main_frame = tk.Frame(self.root, bg='green', padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # Title
        title_label = tk.Label(
            main_frame, 
            text="PUMP PLAYS POKEMON", 
            font=('Arial', 24, 'bold'),
            bg='green',
            fg='white'
        )
        title_label.pack(pady=(0, 20))
        
        # Mode display
        self.mode_label = tk.Label(
            main_frame,
            text="MODE: DEMOCRACY",
            font=('Arial', 16, 'bold'),
            bg='green',
            fg='white'
        )
        self.mode_label.pack(pady=5)
        
        # Mode timer progress bar
        mode_timer_frame = tk.Frame(main_frame, bg='green')
        mode_timer_frame.pack(fill='x', pady=5)
        
        self.mode_timer_label = tk.Label(
            mode_timer_frame,
            text="MODE SWAP TIMER:",
            font=('Arial', 12),
            bg='green',
            fg='white'
        )
        self.mode_timer_label.pack()
        
        self.mode_progress = ttk.Progressbar(
            mode_timer_frame,
            length=400,
            mode='determinate',
            style='White.Horizontal.TProgressbar'
        )
        self.mode_progress.pack(pady=5)
        
        # Configure white progressbar style
        style = ttk.Style()
        style.theme_use('default')
        style.configure('White.Horizontal.TProgressbar',
                       background='white',
                       troughcolor='darkgreen',
                       borderwidth=1,
                       lightcolor='white',
                       darkcolor='white')
        
        # Stats frame
        stats_frame = tk.Frame(main_frame, bg='green')
        stats_frame.pack(fill='x', pady=10)
        
        # Left column
        left_col = tk.Frame(stats_frame, bg='green')
        left_col.pack(side='left', fill='both', expand=True)
        
        self.votes_label = tk.Label(
            left_col,
            text="CURRENT VOTES: 0",
            font=('Arial', 14),
            bg='green',
            fg='white',
            anchor='w'
        )
        self.votes_label.pack(anchor='w')
        
        self.users_label = tk.Label(
            left_col,
            text="ACTIVE USERS: 0",
            font=('Arial', 14),
            bg='green',
            fg='white',
            anchor='w'
        )
        self.users_label.pack(anchor='w')
        
        self.timer_label = tk.Label(
            left_col,
            text="MOVE TIMER: 0s",
            font=('Arial', 14),
            bg='green',
            fg='white',
            anchor='w'
        )
        self.timer_label.pack(anchor='w')
        
        # Right column  
        right_col = tk.Frame(stats_frame, bg='green')
        right_col.pack(side='right', fill='both', expand=True)
        
        self.session_label = tk.Label(
            right_col,
            text="SESSION: 00:00:00",
            font=('Arial', 14),
            bg='green',
            fg='white',
            anchor='e'
        )
        self.session_label.pack(anchor='e')
        
        self.messages_label = tk.Label(
            right_col,
            text="MESSAGES: 0",
            font=('Arial', 14),
            bg='green',
            fg='white',
            anchor='e'
        )
        self.messages_label.pack(anchor='e')
        
        # Last 5 moves table
        moves_frame = tk.Frame(main_frame, bg='green')
        moves_frame.pack(fill='x', pady=10)
        
        moves_title = tk.Label(
            moves_frame,
            text="LAST 5 MOVES:",
            font=('Arial', 14, 'bold'),
            bg='green',
            fg='white'
        )
        moves_title.pack()
        
        # Create table frame with border
        table_frame = tk.Frame(moves_frame, bg='white', bd=2, relief='solid')
        table_frame.pack(pady=5)
        
        # Table headers
        header_frame = tk.Frame(table_frame, bg='green')
        header_frame.pack(fill='x')
        
        tk.Label(header_frame, text="TIME", font=('Arial', 10, 'bold'), bg='green', fg='white', width=10).pack(side='left', padx=2, pady=2)
        tk.Label(header_frame, text="MOVE", font=('Arial', 10, 'bold'), bg='green', fg='white', width=8).pack(side='left', padx=2, pady=2)
        tk.Label(header_frame, text="MODE", font=('Arial', 10, 'bold'), bg='green', fg='white', width=10).pack(side='left', padx=2, pady=2)
        tk.Label(header_frame, text="USER", font=('Arial', 10, 'bold'), bg='green', fg='white', width=15).pack(side='left', padx=2, pady=2)
        
        # Table rows container
        self.moves_table_frame = tk.Frame(table_frame, bg='white')
        self.moves_table_frame.pack(fill='x')
        
        # Vote breakdown
        self.votes_frame = tk.Frame(main_frame, bg='green')
        self.votes_frame.pack(fill='x', pady=20)
        
        votes_title = tk.Label(
            self.votes_frame,
            text="CURRENT VOTES:",
            font=('Arial', 14, 'bold'),
            bg='green',
            fg='white'
        )
        votes_title.pack()
        
        self.vote_details = tk.Label(
            self.votes_frame,
            text="",
            font=('Arial', 12),
            bg='green',
            fg='white',
            justify='left'
        )
        self.vote_details.pack()
        
        # Last move
        self.last_move_label = tk.Label(
            main_frame,
            text="LAST MOVE: NONE",
            font=('Arial', 18, 'bold'),
            bg='green',
            fg='white'
        )
        self.last_move_label.pack(pady=20)
        
        # Start update thread
        self.running = True
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
        self.update_thread.start()
        
    def update_display(self):
        stats = self.chat_handler.get_stats()
        
        # Update mode
        self.mode_label.config(text=f"MODE: {stats['mode'].upper()}")
        
        # Update mode timer progress bar
        mode_progress = (stats['mode_timer'] / stats['mode_swap_interval']) * 100
        self.mode_progress['value'] = mode_progress
        self.mode_timer_label.config(text=f"MODE SWAP TIMER: {stats['mode_timer']}/{stats['mode_swap_interval']}s")
        
        # Update stats
        total_votes = sum(stats['current_votes'].values())
        self.votes_label.config(text=f"CURRENT VOTES: {total_votes}")
        self.users_label.config(text=f"ACTIVE USERS: {stats['user_count']}")
        self.timer_label.config(text=f"MOVE TIMER: {stats['move_timer']}s")
        self.session_label.config(text=f"SESSION: {stats['session_time']}")
        self.messages_label.config(text=f"MESSAGES: {stats['total_messages']}")
        
        # Update last moves table
        self.update_moves_table(stats['last_moves'])
        
        # Update vote breakdown
        if stats['current_votes']:
            vote_text = ""
            for move, count in sorted(stats['current_votes'].items(), key=lambda x: x[1], reverse=True):
                vote_text += f"{move.upper()}: {count}  "
            self.vote_details.config(text=vote_text)
        else:
            self.vote_details.config(text="No votes yet")
    
    def update_moves_table(self, moves):
        # Clear existing rows
        for widget in self.moves_table_frame.winfo_children():
            widget.destroy()
        
        # Add moves (most recent first)
        for i, move in enumerate(reversed(moves[-5:])):  # Show last 5, most recent first
            row_frame = tk.Frame(self.moves_table_frame, bg='white')
            row_frame.pack(fill='x')
            
            # Determine user display
            if move['mode'] == 'democracy':
                user_display = 'by vote'
            else:
                user_display = move['user'][:13] + '...' if len(move['user']) > 15 else move['user']
            
            tk.Label(row_frame, text=move['timestamp'], font=('Arial', 9), bg='white', fg='black', width=10).pack(side='left', padx=2, pady=1)
            tk.Label(row_frame, text=move['move'], font=('Arial', 9, 'bold'), bg='white', fg='black', width=8).pack(side='left', padx=2, pady=1)
            tk.Label(row_frame, text=move['mode'].upper(), font=('Arial', 9), bg='white', fg='black', width=10).pack(side='left', padx=2, pady=1)
            tk.Label(row_frame, text=user_display, font=('Arial', 9), bg='white', fg='black', width=15).pack(side='left', padx=2, pady=1)
    
    def update_loop(self):
        while self.running:
            try:
                self.root.after(0, self.update_display)
                time.sleep(1)
            except:
                break
    
    def run(self):
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        self.running = False
        self.root.quit()