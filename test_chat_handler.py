import asyncio
import random
import time
from datetime import datetime
from chat_handler import PumpChatHandler

class Test<PERSON>umpChatHandler(PumpChatHandler):
    def __init__(self, room_id: str, config: dict):
        super().__init__(room_id, config)
        self.test_usernames = [
            'player1', 'gamer123', 'pokefan', 'trainer_x', 'ash_ketchum',
            'pikachu_lover', 'teamrocket', 'prof_oak', 'brock_rocks', 'misty_water',
            'gary_oak', 'red_champion', 'blue_rival', 'lance_dragon', 'elite_four',
            'gym_leader', 'rocket_grunt', 'nurse_joy', 'officer_jenny', 'bill_pc'
        ]
        self.gameboy_commands = ['a', 'b', 'start', 'select', 'up', 'down', 'left', 'right']
        
    async def connect_and_listen(self):
        """Test version that generates random messages instead of connecting to websocket"""
        print("Connected to test chat (generating random messages)")
        
        # Timer for move intervals and mode swaps
        last_move_check = time.time()
        last_mode_check = time.time()
        message_counter = 0
        
        while True:
            try:
                # Generate random messages every 0.5-2 seconds
                await asyncio.sleep(random.uniform(0.5, 2.0))
                
                # Generate a random message
                if random.random() < 0.7:  # 70% chance of gameboy command
                    message = random.choice(self.gameboy_commands)
                else:  # 30% chance of random chat
                    random_messages = [
                        'let\'s go!', 'nice move!', 'gg', 'pikachu!', 'gotta catch em all',
                        'this is fun', 'random chat message', 'hello world', 'democracy!', 'anarchy!'
                    ]
                    message = random.choice(random_messages)
                
                username = random.choice(self.test_usernames)
                
                # Create fake message data
                fake_message_data = [
                    "newMessage",
                    {
                        "id": f"test-{message_counter}",
                        "roomId": self.room_id,
                        "username": username,
                        "userAddress": f"fake_address_{username}",
                        "message": message,
                        "timestamp": datetime.now().isoformat() + "Z",
                        "messageType": "REGULAR"
                    }
                ]
                
                # Process the fake message
                self.process_message(fake_message_data)
                message_counter += 1
                
                # Update timers
                current_time = time.time()
                self.move_timer = int(current_time - last_move_check)
                self.mode_timer = int(current_time - last_mode_check)
                
                # Check for mode swap
                if self.mode_timer >= self.mode_swap_interval:
                    self.swap_mode()
                    last_mode_check = current_time
                
                # Check for moves based on timer
                winning_move = self.get_winning_move()
                if winning_move:
                    self.execute_move(winning_move)
                    last_move_check = current_time
                    
            except Exception as e:
                print(f"Test handler error: {e}")
                await asyncio.sleep(1)