import asyncio
import websockets
import json
import time
from collections import defaultdict, Counter
from datetime import datetime
import sqlite3
import threading
from typing import Dict, List, Set

class PumpChatHandler:
    def __init__(self, room_id: str, config: dict):
        self.room_id = room_id
        self.config = config
        self.messages = []
        self.current_votes = Counter()
        self.users_voted = set()
        self.mode = "democracy"  # "democracy" or "anarchy"
        self.move_timer = 0
        self.mode_timer = 0
        self.mode_swap_interval = config.get('mode_swap_minutes', 2) * 60  # Convert to seconds
        self.session_start = datetime.now()
        self.last_moves = []  # Track last 5 moves
        self.db_lock = threading.Lock()
        self.init_db()
        
    def init_db(self):
        conn = sqlite3.connect('pump_pokemon.db')
        conn.execute('''
            CREATE TABLE IF NOT EXISTS game_outcomes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME,
                mode TEXT,
                winning_move TEXT,
                vote_count INTEGER,
                user_count INTEGER,
                session_time INTEGER
            )
        ''')
        conn.commit()
        conn.close()
        
    def save_outcome(self, move: str, votes: int, users: int):
        with self.db_lock:
            conn = sqlite3.connect('pump_pokemon.db')
            session_time = int((datetime.now() - self.session_start).total_seconds())
            conn.execute('''
                INSERT INTO game_outcomes 
                (timestamp, mode, winning_move, vote_count, user_count, session_time)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (datetime.now(), self.mode, move, votes, users, session_time))
            conn.commit()
            conn.close()
    
    def parse_gameboy_command(self, message: str) -> str:
        commands = ['a', 'b', 'start', 'select', 'up', 'down', 'left', 'right']
        msg_lower = message.lower().strip()
        for cmd in commands:
            if cmd == msg_lower:
                return cmd
        return None
    
    def swap_mode(self):
        """Swap between democracy and anarchy modes"""
        self.mode = "anarchy" if self.mode == "democracy" else "democracy"
        self.mode_timer = 0  # Reset mode timer
        print(f"Mode automatically changed to: {self.mode}")

    def process_message(self, event_data: dict):
        if event_data[0] == "newMessage":
            msg_data = event_data[1]
            username = msg_data.get('username', '')
            message = msg_data.get('message', '').lower()
            
            # Process gameboy commands
            command = self.parse_gameboy_command(message)
            if command and username not in self.users_voted:
                self.current_votes[command] += 1
                self.users_voted.add(username)
                
            self.messages.append({
                'username': username,
                'message': msg_data.get('message', ''),
                'timestamp': msg_data.get('timestamp', ''),
                'command': command
            })
    
    def get_winning_move(self):
        if not self.current_votes:
            return None
            
        if self.mode == "anarchy":
            # In anarchy, execute first recorded command
            for message in self.messages:
                if message['command']:
                    return message['command']
            return None
        else:
            # In democracy, wait for timer and return most voted command
            if self.move_timer >= self.config['move_interval_seconds']:
                return self.current_votes.most_common(1)[0][0]
        return None
    
    def execute_move(self, move: str):
        vote_count = self.current_votes[move]
        user_count = len(self.users_voted)
        
        print(f"EXECUTE: {move.upper()} (votes: {vote_count}, users: {user_count})")
        
        # Track last moves  
        # Find the user who made the winning move
        winning_user = 'by vote'
        if self.mode == 'anarchy':
            for message in self.messages:
                if message['command'] == move:
                    winning_user = message['username']
                    break
        
        move_data = {
            'move': move.upper(),
            'mode': self.mode,
            'user': winning_user,
            'timestamp': datetime.now().strftime('%H:%M:%S')
        }
        self.last_moves.append(move_data)
        if len(self.last_moves) > 5:
            self.last_moves.pop(0)
        
        # Save to database
        self.save_outcome(move, vote_count, user_count)
        
        # Clear memory - reset for next move
        self.current_votes.clear()
        self.users_voted.clear()
        self.messages.clear()  # Clear messages as requested
        self.move_timer = 0
        
        return move.upper()
    
    def get_stats(self):
        session_time = datetime.now() - self.session_start
        return {
            'mode': self.mode,
            'current_votes': dict(self.current_votes),
            'user_count': len(self.users_voted),
            'session_time': str(session_time).split('.')[0],
            'move_timer': self.move_timer,
            'mode_timer': self.mode_timer,
            'mode_swap_interval': self.mode_swap_interval,
            'total_messages': len(self.messages),
            'last_moves': self.last_moves
        }

    async def connect_and_listen(self):
        ws_url = "wss://livechat.pump.fun/socket.io/?EIO=4&transport=websocket"
        
        async with websockets.connect(
            ws_url,
            additional_headers={
                "Origin": "https://pump.fun",
                "User-Agent": "Mozilla/5.0",
            }
        ) as ws:
            print("Connected to pump.fun chat")
            
            # Timer for move intervals and mode swaps
            last_move_check = time.time()
            last_mode_check = time.time()
            
            async for msg in ws:
                # Handle ping/pong
                if msg == "2":
                    await ws.send("3")
                    continue
                
                # Initial handshake
                if msg.startswith("0"):
                    connect_payload = {
                        "origin": "https://pump.fun",
                        "timestamp": int(time.time() * 1000),
                        "token": None,
                    }
                    await ws.send("40" + json.dumps(connect_payload))
                
                # Join room
                if msg.startswith("40"):
                    join_payload = [
                        "joinRoom",
                        {"roomId": self.room_id, "username": ""}
                    ]
                    await ws.send("42" + json.dumps(join_payload))
                
                # Process messages
                if msg.startswith("42"):
                    try:
                        data = json.loads(msg[2:])
                        self.process_message(data)
                    except Exception as e:
                        print(f"Parse error: {e}")
                
                # Update timers
                current_time = time.time()
                self.move_timer = int(current_time - last_move_check)
                self.mode_timer = int(current_time - last_mode_check)
                
                # Check for mode swap
                if self.mode_timer >= self.mode_swap_interval:
                    self.swap_mode()
                    last_mode_check = current_time
                
                # Check for moves based on timer
                winning_move = self.get_winning_move()
                if winning_move:
                    self.execute_move(winning_move)
                    last_move_check = current_time