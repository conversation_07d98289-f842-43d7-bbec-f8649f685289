import asyncio
import websockets
import json
import time

WS_URL = "wss://livechat.pump.fun/socket.io/?EIO=4&transport=websocket"
ROOM_ID = "2pdgDGarGrPD5y7W2BeHmz1RZjke6UouKNeDjGM3pump"

async def pumpfun_chat():
    async with websockets.connect(
        WS_URL,
        additional_headers={
            "Origin": "https://pump.fun",
            "User-Agent": "Mozilla/5.0",
        }
    ) as ws:
        print("Connected.")

        async for msg in ws:
            print("recv:", msg)

            # Engine.IO ping (server -> client)
            if msg == "2":
                await ws.send("3")  # pong
                continue

            # Initial handshake
            if msg.startswith("0"):
                data = json.loads(msg[1:])
                print("Handshake:", data)

                # Send connect
                connect_payload = {
                    "origin": "https://pump.fun",
                    "timestamp": int(time.time() * 1000),
                    "token": None,
                }
                await ws.send("40" + json.dumps(connect_payload))
                print("sent connect")

            # Socket.IO connect ack
            if msg.startswith("40"):
                print("Connected to namespace, now joining room...")
                join_payload = [
                    "joinRoom",
                    {
                        "roomId": ROOM_ID,
                        "username": ""  # anonymous
                    }
                ]
                await ws.send("42" + json.dumps(join_payload))
                print("sent joinRoom")

            # Handle setCookie event
            if msg.startswith("42"):
                try:
                    data = json.loads(msg[2:])
                    print("event:", data)
                except Exception as e:
                    print("parse error:", e)


if __name__ == "__main__":
    asyncio.run(pumpfun_chat())
